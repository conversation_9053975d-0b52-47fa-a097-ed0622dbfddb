import React, { useState, useEffect } from 'react';
import UserNav from "../Components/UserNav";
import {
    IoLocationOutline,
    IoLinkOutline,
    IoCalendarOutline,
    IoGridOutline,
    IoBookmarkOutline,
    IoPeopleOutline,
    IoPersonAddOutline,
    IoSettingsOutline,
    IoShareOutline,
    IoHeartOutline,
    IoChatbubbleOutline,
    IoEyeOutline,
    IoTrendingUpOutline,
    IoStatsChartOutline,
    IoImageOutline,
    IoVideocamOutline,
    IoDocumentTextOutline,
    IoRefreshOutline,
    IoCloudUploadOutline
} from "react-icons/io5";
import Post from "../Components/Post";
import { useSelector, useDispatch } from 'react-redux';
import { toggleSavePost, updateUserProfile } from '../Reducer/Actions';
import Image from '../Components/image';
import api from '../services/api';

const Profile = () => {
    const [activeTab, setActiveTab] = useState('posts');
    const [loading, setLoading] = useState(true);
    const [postsLoading, setPostsLoading] = useState(false);
    const [error, setError] = useState(null);
    const [profileData, setProfileData] = useState(null);
    const [userPosts, setUserPosts] = useState([]);
    const [savedPostsData, setSavedPostsData] = useState([]);
    const [followers, setFollowers] = useState([]);
    const [following, setFollowing] = useState([]);
    const [profileStats, setProfileStats] = useState({
        totalPosts: 0,
        totalLikes: 0,
        totalViews: 0,
        totalComments: 0,
        followersCount: 0,
        followingCount: 0
    });
    const [isEditingProfile, setIsEditingProfile] = useState(false);
    const [editForm, setEditForm] = useState({
        nom: '',
        prenom: '',
        bio: '',
        location: '',
        website: '',
        profil: null,
        cover_image: null
    });

    const user = useSelector((state) => state.user);
    const dispatch = useDispatch();

    // Fetch profile data from backend
    const fetchProfileData = async () => {
        // Prevent multiple simultaneous requests
        if (loading) return;

        try {
            setLoading(true);
            setError(null);

            // Fetch user profile
            const profileResponse = await api.get('/profile');
            setProfileData(profileResponse.data);

            // Update Redux store with fresh profile data
            dispatch(updateUserProfile(profileResponse.data));

            // Initialize edit form with current data only if not already editing
            if (!isEditingProfile) {
                setEditForm({
                    nom: profileResponse.data.nom || '',
                    prenom: profileResponse.data.prenom || '',
                    bio: profileResponse.data.bio || '',
                    location: profileResponse.data.location || '',
                    website: profileResponse.data.website || '',
                    profil: null,
                    cover_image: null
                });
            }

        } catch (err) {
            console.error('Error fetching profile data:', err);
            setError('Failed to load profile data. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    // Separate function to fetch posts
    const fetchUserPosts = async () => {
        if (!profileData?.id) return;

        try {
            setPostsLoading(true);

            // Fetch user's posts
            const postsResponse = await api.get('/posts');
            const userPostsFiltered = postsResponse.data.filter(post =>
                post.user_id === profileData.id || post.user?.id === profileData.id
            );
            setUserPosts(userPostsFiltered);

            // Calculate stats from posts
            const totalLikes = userPostsFiltered.reduce((sum, post) => sum + (post.likes || 0), 0);
            const totalComments = userPostsFiltered.reduce((sum, post) => sum + (post.comments?.length || 0), 0);
            const totalViews = userPostsFiltered.reduce((sum, post) => sum + (post.views || 0), 0);

            setProfileStats(prev => ({
                ...prev,
                totalPosts: userPostsFiltered.length,
                totalLikes,
                totalViews,
                totalComments
            }));

        } catch (err) {
            console.error('Error fetching posts:', err);
        } finally {
            setPostsLoading(false);
        }
    };

    // Fetch saved posts (mock implementation - you can implement this in backend)
    const fetchSavedPosts = async () => {
        try {
            // For now, we'll use the saved posts from Redux
            // In a real implementation, you'd have a backend endpoint for saved posts
            const allPostsResponse = await api.get('/posts');
            const savedPostIds = JSON.parse(localStorage.getItem('savedPosts') || '[]');
            const savedPostsFiltered = allPostsResponse.data.filter(post =>
                savedPostIds.includes(post.id)
            );
            setSavedPostsData(savedPostsFiltered);
        } catch (err) {
            console.error('Error fetching saved posts:', err);
        }
    };

    // Handle profile update
    const handleProfileUpdate = async (e) => {
        e.preventDefault();
        e.stopPropagation();

        try {
            setLoading(true);

            const formData = new FormData();
            Object.keys(editForm).forEach(key => {
                if (editForm[key] !== null && editForm[key] !== '') {
                    formData.append(key, editForm[key]);
                }
            });

            const response = await api.post('/profile/update', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });

            setProfileData(response.data.user);
            dispatch(updateUserProfile(response.data.user));
            setIsEditingProfile(false);

            // Refresh profile data after update
            await fetchProfileData();

        } catch (err) {
            console.error('Error updating profile:', err);
            setError('Failed to update profile. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    // Handle file input changes
    const handleFileChange = (e, fieldName) => {
        const file = e.target.files[0];
        if (file) {
            setEditForm(prev => ({
                ...prev,
                [fieldName]: file
            }));
        }
    };

    useEffect(() => {
        if (user && user.id) {
            fetchProfileData();
            fetchSavedPosts();
        }
    }, [user?.id]); // Only depend on user.id to prevent infinite loops

    // Fetch posts when profile data is available
    useEffect(() => {
        if (profileData?.id) {
            fetchUserPosts();
        }
    }, [profileData?.id]);

    const currentUser = profileData || user;

    // Loading component
    if (loading && !profileData) {
        return (
            <section className="min-h-screen bg-gray">
                <UserNav />
                <div className="flex items-center justify-center min-h-[60vh]">
                    <div className="text-center">
                        <IoRefreshOutline className="w-12 h-12 text-secondary animate-spin mx-auto mb-4" />
                        <p className="text-gray-600">Loading profile...</p>
                    </div>
                </div>
            </section>
        );
    }

    // Error component
    if (error) {
        return (
            <section className="min-h-screen bg-gray">
                <UserNav />
                <div className="flex items-center justify-center min-h-[60vh]">
                    <div className="text-center">
                        <div className="bg-red-100 text-red-600 p-4 rounded-lg mb-4">
                            {error}
                        </div>
                        <button
                            onClick={fetchProfileData}
                            className="px-4 py-2 bg-secondary text-white rounded-lg hover:bg-secondary-dark"
                        >
                            Try Again
                        </button>
                    </div>
                </div>
            </section>
        );
    }

    const TabButton = ({ id, label, icon: Icon, count }) => (
        <button
            onClick={() => setActiveTab(id)}
            className={`flex items-center gap-2 px-4 py-2 font-medium rounded-xl transition-all duration-200
                ${activeTab === id
                    ? 'bg-secondary text-white'
                    : 'text-gray-600 hover:bg-gray-100'}`}
        >
            <Icon className="text-xl" />
            <span>{label}</span>
            {count > 0 && (
                <span className={`text-sm ${activeTab === id ? 'bg-white text-secondary' : 'bg-gray-200'} px-2 py-0.5 rounded-full`}>
                    {count}
                </span>
            )}
        </button>
    );

    const StatsCard = ({ icon: Icon, label, value, color = "text-secondary" }) => (
        <div className="bg-white rounded-xl p-4 shadow-sm">
            <div className="flex items-center gap-3">
                <div className={`p-2 rounded-lg bg-gray-100`}>
                    <Icon className={`w-6 h-6 ${color}`} />
                </div>
                <div>
                    <div className="text-2xl font-bold text-gray-900">{value}</div>
                    <div className="text-sm text-gray-500">{label}</div>
                </div>
            </div>
        </div>
    );

    const SavedPostCard = ({ post }) => {
        const postImage = post.images && post.images.length > 0
            ? (post.images[0].image || post.images[0].path)
            : post.media && post.media.length > 0
                ? post.media[0]
                : '/default-post-image.jpg';

        const handleUnsave = () => {
            dispatch(toggleSavePost(post.id));
            // Update local state
            setSavedPostsData(prev => prev.filter(p => p.id !== post.id));
            // Update localStorage
            const savedPosts = JSON.parse(localStorage.getItem('savedPosts') || '[]');
            const updatedSavedPosts = savedPosts.filter(id => id !== post.id);
            localStorage.setItem('savedPosts', JSON.stringify(updatedSavedPosts));
        };

        return (
            <div className="bg-white rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                <div className="relative group">
                    <img
                        src={postImage}
                        alt={post.titre || post.description || 'Post image'}
                        className="w-full h-48 object-cover"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 flex items-center justify-center">
                        <button
                            onClick={handleUnsave}
                            className="opacity-0 group-hover:opacity-100 bg-white text-secondary px-4 py-2 rounded-full font-medium transform translate-y-2 group-hover:translate-y-0 transition-all duration-200 flex items-center gap-2"
                        >
                            <IoBookmarkOutline className="w-4 h-4" />
                            Unsave
                        </button>
                    </div>
                    <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded-full text-xs">
                        {post.images?.length || post.media?.length || 1} photo{(post.images?.length || post.media?.length || 1) > 1 ? 's' : ''}
                    </div>
                </div>
                <div className="p-4">
                    <h3 className="font-semibold text-lg mb-2 line-clamp-1">
                        {post.titre || post.description?.substring(0, 50) || 'Untitled Post'}
                    </h3>
                    <p className="text-gray-600 text-sm line-clamp-2 mb-3">
                        {post.content || post.description || 'No description available'}
                    </p>
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            <img
                                src={post.user?.profil || post.user?.profilePic || '/default-avatar.jpg'}
                                alt={post.user?.name || post.user?.nom || 'User'}
                                className="w-6 h-6 rounded-full object-cover"
                            />
                            <span className="text-sm text-gray-500">
                                {post.user?.name || `${post.user?.nom} ${post.user?.prenom}` || 'Unknown User'}
                            </span>
                        </div>
                        <div className="flex items-center gap-3 text-xs text-gray-400">
                            {post.likes && (
                                <div className="flex items-center gap-1">
                                    <IoHeartOutline className="w-3 h-3" />
                                    {post.likes}
                                </div>
                            )}
                            {post.location && (
                                <div className="flex items-center gap-1">
                                    <IoLocationOutline className="w-3 h-3" />
                                    {post.location.substring(0, 15)}...
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        );
    };

    return (
        <section className="min-h-screen bg-gray">
            <UserNav />
            
            {/* Profile Header */}
            <div className="relative">
                {/* Cover Photo */}
                <div className="h-64 w-full bg-gradient-to-r from-primary to-secondary relative">
                    <Image
                        src={currentUser?.cover_image || currentUser?.cover || '/default-cover.jpg'}
                        alt="Cover"
                        className="w-full h-full object-cover"
                    />
                    <button
                        type="button"
                        onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            setIsEditingProfile(true);
                        }}
                        className="absolute top-4 right-4 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all"
                    >
                        <IoCloudUploadOutline className="w-5 h-5" />
                    </button>
                </div>

                {/* Profile Info Card */}
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="bg-white rounded-xl shadow-sm -mt-24 p-6">
                        <div className="flex flex-col lg:flex-row items-start lg:items-center gap-6">
                            {/* Profile Picture */}
                            <div className="relative">
                                <Image
                                    src={currentUser?.profil || '/default-avatar.jpg'}
                                    alt={currentUser?.nom || currentUser?.name || 'User'}
                                    className="w-32 h-32 rounded-full border-4 border-white shadow-md object-cover"
                                />
                                <button
                                    type="button"
                                    onClick={(e) => {
                                        e.preventDefault();
                                        e.stopPropagation();
                                        setIsEditingProfile(true);
                                    }}
                                    className="absolute bottom-0 right-0 bg-secondary text-white p-2 rounded-full shadow-lg hover:bg-secondary-dark transition-colors"
                                >
                                    <IoCloudUploadOutline className="w-5 h-5" />
                                </button>
                            </div>

                            {/* User Info */}
                            <div className="flex-1">
                                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                                    <div>
                                        <h1 className="text-3xl font-bold text-gray-900">
                                            {currentUser?.nom && currentUser?.prenom
                                                ? `${currentUser.nom} ${currentUser.prenom}`
                                                : currentUser?.name || 'User'}
                                        </h1>
                                        <p className="text-gray-500">@{currentUser?.username || 'username'}</p>
                                        {currentUser?.email && (
                                            <p className="text-sm text-gray-400">{currentUser.email}</p>
                                        )}
                                    </div>
                                    <div className="flex gap-2">
                                        <button
                                            type="button"
                                            onClick={(e) => {
                                                e.preventDefault();
                                                e.stopPropagation();
                                                setIsEditingProfile(true);
                                            }}
                                            className="px-6 py-2 bg-secondary text-white rounded-xl hover:bg-secondary-dark transition-colors flex items-center gap-2"
                                        >
                                            <IoSettingsOutline className="w-4 h-4" />
                                            Edit Profile
                                        </button>
                                        <button
                                            type="button"
                                            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors"
                                        >
                                            <IoShareOutline className="w-4 h-4" />
                                        </button>
                                    </div>
                                </div>

                                <p className="mt-4 text-gray-600">{currentUser?.bio || 'No bio available'}</p>

                                <div className="mt-4 flex flex-wrap gap-4">
                                    {currentUser?.location && (
                                        <div className="flex items-center gap-1 text-gray-500">
                                            <IoLocationOutline />
                                            <span>{currentUser.location}</span>
                                        </div>
                                    )}
                                    {currentUser?.website && (
                                        <div className="flex items-center gap-1 text-secondary">
                                            <IoLinkOutline />
                                            <a href={currentUser.website} target="_blank" rel="noopener noreferrer" className="hover:underline">
                                                {currentUser.website}
                                            </a>
                                        </div>
                                    )}
                                    <div className="flex items-center gap-1 text-gray-500">
                                        <IoCalendarOutline />
                                        <span>Joined {currentUser?.created_at ? new Date(currentUser.created_at).toLocaleDateString() : 'Recently'}</span>
                                    </div>
                                </div>

                                {/* Enhanced Stats */}
                                <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
                                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                                        <div className="text-2xl font-bold text-gray-900">{profileStats.totalPosts}</div>
                                        <div className="text-sm text-gray-500">Posts</div>
                                    </div>
                                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                                        <div className="text-2xl font-bold text-gray-900">{profileStats.followersCount}</div>
                                        <div className="text-sm text-gray-500">Followers</div>
                                    </div>
                                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                                        <div className="text-2xl font-bold text-gray-900">{profileStats.followingCount}</div>
                                        <div className="text-sm text-gray-500">Following</div>
                                    </div>
                                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                                        <div className="text-2xl font-bold text-gray-900">{profileStats.totalLikes}</div>
                                        <div className="text-sm text-gray-500">Likes</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Enhanced Stats Cards */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                    <StatsCard
                        icon={IoStatsChartOutline}
                        label="Total Views"
                        value={profileStats.totalViews.toLocaleString()}
                        color="text-blue-600"
                    />
                    <StatsCard
                        icon={IoHeartOutline}
                        label="Total Likes"
                        value={profileStats.totalLikes.toLocaleString()}
                        color="text-red-500"
                    />
                    <StatsCard
                        icon={IoChatbubbleOutline}
                        label="Comments"
                        value={profileStats.totalComments.toLocaleString()}
                        color="text-green-600"
                    />
                    <StatsCard
                        icon={IoTrendingUpOutline}
                        label="Engagement"
                        value={`${((profileStats.totalLikes + profileStats.totalComments) / Math.max(profileStats.totalPosts, 1)).toFixed(1)}`}
                        color="text-purple-600"
                    />
                </div>
            </div>

            {/* Content Tabs */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="bg-white rounded-xl shadow-sm p-4 mb-6">
                    <div className="flex gap-4 flex-wrap">
                        <TabButton
                            id="posts"
                            label="Posts"
                            icon={IoGridOutline}
                            count={profileStats.totalPosts}
                        />
                        <TabButton
                            id="saved"
                            label="Saved"
                            icon={IoBookmarkOutline}
                            count={savedPostsData.length}
                        />
                        <TabButton
                            id="media"
                            label="Media"
                            icon={IoImageOutline}
                            count={userPosts.filter(post => post.images && post.images.length > 0).length}
                        />
                        <TabButton
                            id="analytics"
                            label="Analytics"
                            icon={IoStatsChartOutline}
                            count={0}
                        />
                    </div>
                </div>

                {/* Posts Grid */}
                {activeTab === 'posts' && (
                    <div className="space-y-6 max-w-2xl mx-auto">
                        {postsLoading ? (
                            <div className="text-center py-8">
                                <IoRefreshOutline className="w-8 h-8 text-secondary animate-spin mx-auto mb-2" />
                                <p className="text-gray-600">Loading posts...</p>
                            </div>
                        ) : userPosts.length > 0 ? (
                            userPosts.map((post) => (
                                <Post key={post.id} post={post} />
                            ))
                        ) : (
                            <div className="text-center py-12">
                                <div className="inline-block p-4 rounded-full bg-gray-100 mb-4">
                                    <IoDocumentTextOutline className="w-8 h-8 text-gray-400" />
                                </div>
                                <h3 className="text-lg font-medium text-gray-900">No posts yet</h3>
                                <p className="mt-2 text-gray-500">
                                    Start sharing your travel experiences!
                                </p>
                            </div>
                        )}
                    </div>
                )}

                {/* Saved Posts Grid */}
                {activeTab === 'saved' && (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {savedPostsData.length > 0 ? (
                            savedPostsData.map((post) => (
                                <SavedPostCard key={post.id} post={post} />
                            ))
                        ) : (
                            <div className="col-span-full text-center py-12">
                                <div className="inline-block p-4 rounded-full bg-gray-100 mb-4">
                                    <IoBookmarkOutline className="w-8 h-8 text-gray-400" />
                                </div>
                                <h3 className="text-lg font-medium text-gray-900">No saved posts yet</h3>
                                <p className="mt-2 text-gray-500">
                                    When you save posts, they'll appear here.
                                </p>
                            </div>
                        )}
                    </div>
                )}

                {/* Media Grid */}
                {activeTab === 'media' && (
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                        {userPosts
                            .filter(post => post.images && post.images.length > 0)
                            .flatMap(post => post.images.map(image => ({ ...image, post })))
                            .map((media, index) => (
                                <div key={index} className="relative group aspect-square">
                                    <img
                                        src={media.image || media.path}
                                        alt="Media"
                                        className="w-full h-full object-cover rounded-lg"
                                    />
                                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 rounded-lg flex items-center justify-center">
                                        <div className="opacity-0 group-hover:opacity-100 text-white text-center">
                                            <IoEyeOutline className="w-6 h-6 mx-auto mb-1" />
                                            <p className="text-xs">{media.post.description?.substring(0, 30)}...</p>
                                        </div>
                                    </div>
                                </div>
                            ))
                        }
                        {userPosts.filter(post => post.images && post.images.length > 0).length === 0 && (
                            <div className="col-span-full text-center py-12">
                                <div className="inline-block p-4 rounded-full bg-gray-100 mb-4">
                                    <IoImageOutline className="w-8 h-8 text-gray-400" />
                                </div>
                                <h3 className="text-lg font-medium text-gray-900">No media yet</h3>
                                <p className="mt-2 text-gray-500">
                                    Share photos and videos to see them here.
                                </p>
                            </div>
                        )}
                    </div>
                )}

                {/* Analytics Tab */}
                {activeTab === 'analytics' && (
                    <div className="bg-white rounded-xl shadow-sm p-6">
                        <h3 className="text-xl font-bold text-gray-900 mb-6">Profile Analytics</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-6 rounded-xl">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-blue-100">Total Reach</p>
                                        <p className="text-2xl font-bold">{(profileStats.totalViews * 1.5).toLocaleString()}</p>
                                    </div>
                                    <IoTrendingUpOutline className="w-8 h-8 text-blue-200" />
                                </div>
                            </div>
                            <div className="bg-gradient-to-r from-green-500 to-green-600 text-white p-6 rounded-xl">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-green-100">Engagement Rate</p>
                                        <p className="text-2xl font-bold">
                                            {profileStats.totalPosts > 0
                                                ? `${(((profileStats.totalLikes + profileStats.totalComments) / profileStats.totalPosts) * 100).toFixed(1)}%`
                                                : '0%'
                                            }
                                        </p>
                                    </div>
                                    <IoStatsChartOutline className="w-8 h-8 text-green-200" />
                                </div>
                            </div>
                            <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-6 rounded-xl">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-purple-100">Avg. Likes/Post</p>
                                        <p className="text-2xl font-bold">
                                            {profileStats.totalPosts > 0
                                                ? Math.round(profileStats.totalLikes / profileStats.totalPosts)
                                                : 0
                                            }
                                        </p>
                                    </div>
                                    <IoHeartOutline className="w-8 h-8 text-purple-200" />
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>

            {/* Profile Edit Modal */}
            {isEditingProfile && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                    <div className="bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                        <div className="p-6">
                            <div className="flex items-center justify-between mb-6">
                                <h2 className="text-2xl font-bold text-gray-900">Edit Profile</h2>
                                <button
                                    onClick={() => setIsEditingProfile(false)}
                                    className="text-gray-500 hover:text-gray-700"
                                >
                                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>

                            <form onSubmit={handleProfileUpdate} className="space-y-6">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            First Name
                                        </label>
                                        <input
                                            type="text"
                                            value={editForm.prenom}
                                            onChange={(e) => setEditForm(prev => ({ ...prev, prenom: e.target.value }))}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-secondary focus:border-transparent"
                                            placeholder="Enter your first name"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Last Name
                                        </label>
                                        <input
                                            type="text"
                                            value={editForm.nom}
                                            onChange={(e) => setEditForm(prev => ({ ...prev, nom: e.target.value }))}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-secondary focus:border-transparent"
                                            placeholder="Enter your last name"
                                        />
                                    </div>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Bio
                                    </label>
                                    <textarea
                                        value={editForm.bio}
                                        onChange={(e) => setEditForm(prev => ({ ...prev, bio: e.target.value }))}
                                        rows={4}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-secondary focus:border-transparent"
                                        placeholder="Tell us about yourself..."
                                    />
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Location
                                        </label>
                                        <input
                                            type="text"
                                            value={editForm.location}
                                            onChange={(e) => setEditForm(prev => ({ ...prev, location: e.target.value }))}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-secondary focus:border-transparent"
                                            placeholder="Your location"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Website
                                        </label>
                                        <input
                                            type="url"
                                            value={editForm.website}
                                            onChange={(e) => setEditForm(prev => ({ ...prev, website: e.target.value }))}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-secondary focus:border-transparent"
                                            placeholder="https://your-website.com"
                                        />
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Profile Picture
                                        </label>
                                        <input
                                            type="file"
                                            accept="image/*"
                                            onChange={(e) => handleFileChange(e, 'profil')}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-secondary focus:border-transparent"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Cover Image
                                        </label>
                                        <input
                                            type="file"
                                            accept="image/*"
                                            onChange={(e) => handleFileChange(e, 'cover_image')}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-secondary focus:border-transparent"
                                        />
                                    </div>
                                </div>

                                <div className="flex gap-4 pt-4">
                                    <button
                                        type="submit"
                                        disabled={loading}
                                        className="flex-1 bg-secondary text-white py-2 px-4 rounded-lg hover:bg-secondary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                                    >
                                        {loading ? (
                                            <>
                                                <IoRefreshOutline className="w-4 h-4 animate-spin" />
                                                Updating...
                                            </>
                                        ) : (
                                            'Save Changes'
                                        )}
                                    </button>
                                    <button
                                        type="button"
                                        onClick={() => setIsEditingProfile(false)}
                                        className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                                    >
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            )}
        </section>
    );
};

export default Profile;

