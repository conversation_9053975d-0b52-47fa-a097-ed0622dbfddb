[2025-06-12 21:35:21] local.INFO: user Id:   
[2025-06-12 21:35:22] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'user_id' cannot be null (Connection: mysql, SQL: insert into `posts` (`description`, `location`, `user_id`, `map_url`, `updated_at`, `created_at`) values (The issue seems to be with the route configuration. The route file appears to be correct, but the problem might be with the route caching.

To resolve this issue, you can try clearing the route cache by running the following command in your terminal:, ?, ?, https://www.google.com/maps/search/?api=1&query=tiflet, 2025-06-12 21:35:22, 2025-06-12 21:35:22)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'user_id' cannot be null (Connection: mysql, SQL: insert into `posts` (`description`, `location`, `user_id`, `map_url`, `updated_at`, `created_at`) values (The issue seems to be with the route configuration. The route file appears to be correct, but the problem might be with the route caching.



To resolve this issue, you can try clearing the route cache by running the following command in your terminal:, ?, ?, https://www.google.com/maps/search/?api=1&query=tiflet, 2025-06-12 21:35:22, 2025-06-12 21:35:22)) at C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert()
#3 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId()
#4 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId()
#5 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call()
#6 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#7 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert()
#8 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\app\\Http\\Controllers\\PostController.php(57): Illuminate\\Database\\Eloquent\\Model->save()
#9 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\PostController->store()
#10 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#11 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#12 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#13 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#14 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#16 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#18 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#19 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#20 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#21 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#22 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#23 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#25 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#26 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#28 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#29 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#31 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#33 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#35 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#37 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#39 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#41 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#43 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#44 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#45 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#46 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#47 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'user_id' cannot be null at C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:53)
[stacktrace]
#0 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(53): PDOStatement->execute()
#1 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}()
#2 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert()
#5 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId()
#6 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId()
#7 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call()
#8 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#9 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert()
#10 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\app\\Http\\Controllers\\PostController.php(57): Illuminate\\Database\\Eloquent\\Model->save()
#11 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\PostController->store()
#12 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#13 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#16 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#18 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#20 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#21 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#22 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#23 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#24 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#25 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#27 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#28 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#30 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#31 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#33 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#35 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#37 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#39 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#41 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#43 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#45 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#46 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#47 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#48 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#49 {main}
"} 
[2025-06-12 21:36:38] local.INFO: user Id:   
[2025-06-12 21:36:38] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'user_id' cannot be null (Connection: mysql, SQL: insert into `posts` (`description`, `location`, `user_id`, `map_url`, `updated_at`, `created_at`) values (tgvsdb, ?, ?, https://www.google.com/maps/search/?api=1&query=uycvhjab, 2025-06-12 21:36:38, 2025-06-12 21:36:38)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'user_id' cannot be null (Connection: mysql, SQL: insert into `posts` (`description`, `location`, `user_id`, `map_url`, `updated_at`, `created_at`) values (tgvsdb, ?, ?, https://www.google.com/maps/search/?api=1&query=uycvhjab, 2025-06-12 21:36:38, 2025-06-12 21:36:38)) at C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert()
#3 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId()
#4 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId()
#5 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call()
#6 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#7 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert()
#8 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\app\\Http\\Controllers\\PostController.php(57): Illuminate\\Database\\Eloquent\\Model->save()
#9 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\PostController->store()
#10 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#11 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#12 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#13 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#14 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#16 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#18 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#19 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#20 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#21 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#22 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#23 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#25 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#26 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#28 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#29 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#31 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#33 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#35 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#37 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#39 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#41 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#43 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#44 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#45 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#46 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#47 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'user_id' cannot be null at C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:53)
[stacktrace]
#0 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(53): PDOStatement->execute()
#1 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}()
#2 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert()
#5 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId()
#6 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId()
#7 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call()
#8 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#9 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert()
#10 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\app\\Http\\Controllers\\PostController.php(57): Illuminate\\Database\\Eloquent\\Model->save()
#11 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\PostController->store()
#12 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#13 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#16 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#18 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#20 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#21 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#22 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#23 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#24 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#25 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#27 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#28 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#30 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#31 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#33 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#35 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#37 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#39 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#41 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#43 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#45 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#46 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#47 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#48 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#49 {main}
"} 
[2025-06-12 21:37:35] local.INFO: user Id:   
[2025-06-12 21:37:35] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'user_id' cannot be null (Connection: mysql, SQL: insert into `posts` (`description`, `location`, `user_id`, `map_url`, `updated_at`, `created_at`) values (tgvsdb, ?, ?, https://www.google.com/maps/search/?api=1&query=uycvhjab, 2025-06-12 21:37:35, 2025-06-12 21:37:35)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'user_id' cannot be null (Connection: mysql, SQL: insert into `posts` (`description`, `location`, `user_id`, `map_url`, `updated_at`, `created_at`) values (tgvsdb, ?, ?, https://www.google.com/maps/search/?api=1&query=uycvhjab, 2025-06-12 21:37:35, 2025-06-12 21:37:35)) at C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert()
#3 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId()
#4 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId()
#5 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call()
#6 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#7 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert()
#8 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\app\\Http\\Controllers\\PostController.php(57): Illuminate\\Database\\Eloquent\\Model->save()
#9 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\PostController->store()
#10 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#11 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#12 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#13 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#14 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#16 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#18 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#19 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#20 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#21 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#22 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#23 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#25 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#26 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#28 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#29 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#31 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#33 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#35 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#37 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#39 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#41 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#43 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#44 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#45 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#46 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#47 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'user_id' cannot be null at C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:53)
[stacktrace]
#0 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(53): PDOStatement->execute()
#1 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}()
#2 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert()
#5 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId()
#6 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId()
#7 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call()
#8 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#9 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert()
#10 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\app\\Http\\Controllers\\PostController.php(57): Illuminate\\Database\\Eloquent\\Model->save()
#11 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\PostController->store()
#12 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#13 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#16 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#18 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#20 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#21 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#22 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#23 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#24 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#25 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#27 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#28 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#30 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#31 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#33 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#35 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#37 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#39 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#41 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#43 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#45 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#46 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#47 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#48 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#49 {main}
"} 
[2025-06-12 21:39:22] local.INFO: Auth check: false  
[2025-06-12 21:39:22] local.INFO: Auth ID:   
[2025-06-12 21:39:22] local.INFO: Auth user:  ["No user"] 
[2025-06-12 21:39:22] local.INFO: Final user ID set:   
[2025-06-12 21:39:22] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'id_user' in 'field list' (Connection: mysql, SQL: insert into `posts` (`description`, `location`, `id_user`, `map_url`, `updated_at`, `created_at`) values (tgvsdb, ?, ?, https://www.google.com/maps/search/?api=1&query=uycvhjab, 2025-06-12 21:39:22, 2025-06-12 21:39:22)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'id_user' in 'field list' (Connection: mysql, SQL: insert into `posts` (`description`, `location`, `id_user`, `map_url`, `updated_at`, `created_at`) values (tgvsdb, ?, ?, https://www.google.com/maps/search/?api=1&query=uycvhjab, 2025-06-12 21:39:22, 2025-06-12 21:39:22)) at C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert()
#3 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId()
#4 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId()
#5 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call()
#6 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#7 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert()
#8 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\app\\Http\\Controllers\\PostController.php(64): Illuminate\\Database\\Eloquent\\Model->save()
#9 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\PostController->store()
#10 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#11 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#12 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#13 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#14 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#16 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#18 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#19 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#20 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#21 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#22 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#23 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#25 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#26 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#28 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#29 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#31 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#33 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#35 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#37 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#39 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#41 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#43 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#44 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#45 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#46 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#47 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'id_user' in 'field list' at C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:47)
[stacktrace]
#0 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(47): PDO->prepare()
#1 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}()
#2 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert()
#5 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId()
#6 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId()
#7 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call()
#8 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#9 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert()
#10 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\app\\Http\\Controllers\\PostController.php(64): Illuminate\\Database\\Eloquent\\Model->save()
#11 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\PostController->store()
#12 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#13 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#16 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#18 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#20 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#21 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#22 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#23 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#24 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#25 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#27 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#28 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#30 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#31 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#33 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#35 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#37 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#39 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#41 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#43 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#45 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#46 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#47 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#48 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#49 {main}
"} 
[2025-06-12 21:40:55] local.INFO: Auth check: true  
[2025-06-12 21:40:55] local.INFO: Auth ID: 1  
[2025-06-12 21:40:55] local.INFO: Auth user:  {"id":1,"username":"Ibtissam1000","nom":"btissam","prenom":"elhabchi","date_naissence":null,"email":"<EMAIL>","profil":"/storage/profile_pic/default.jpg","cover":null,"sex":"homme","email_verified_at":null,"created_at":"2025-06-12T21:14:46.000000Z","updated_at":"2025-06-12T21:14:46.000000Z"} 
[2025-06-12 21:40:55] local.INFO: Final user ID set: 1  
[2025-06-12 21:40:55] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'id_user' in 'field list' (Connection: mysql, SQL: insert into `posts` (`description`, `location`, `id_user`, `map_url`, `updated_at`, `created_at`) values (jncsdjk, ?, 1, https://www.google.com/maps/search/?api=1&query=tanger, 2025-06-12 21:40:55, 2025-06-12 21:40:55)) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'id_user' in 'field list' (Connection: mysql, SQL: insert into `posts` (`description`, `location`, `id_user`, `map_url`, `updated_at`, `created_at`) values (jncsdjk, ?, 1, https://www.google.com/maps/search/?api=1&query=tanger, 2025-06-12 21:40:55, 2025-06-12 21:40:55)) at C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert()
#3 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId()
#4 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId()
#5 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call()
#6 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#7 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert()
#8 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\app\\Http\\Controllers\\PostController.php(64): Illuminate\\Database\\Eloquent\\Model->save()
#9 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\PostController->store()
#10 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#11 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#12 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#13 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#14 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#16 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#18 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#20 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#21 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#22 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#23 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#24 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#25 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#27 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#28 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#30 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#31 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#33 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#35 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#37 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#39 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#41 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#43 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#45 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#46 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#47 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#48 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#49 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'id_user' in 'field list' at C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:47)
[stacktrace]
#0 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(47): PDO->prepare()
#1 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}()
#2 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert()
#5 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId()
#6 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId()
#7 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call()
#8 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#9 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert()
#10 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\app\\Http\\Controllers\\PostController.php(64): Illuminate\\Database\\Eloquent\\Model->save()
#11 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\PostController->store()
#12 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#13 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#16 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#18 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#20 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#22 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#23 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#24 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#25 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#26 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#27 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#29 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#30 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#32 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#33 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#35 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#37 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#39 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#41 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#43 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#45 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#47 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#48 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#49 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#50 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#51 {main}
"} 
[2025-06-12 21:41:39] local.INFO: Auth check: true  
[2025-06-12 21:41:39] local.INFO: Auth ID: 1  
[2025-06-12 21:41:39] local.INFO: Auth user:  {"id":1,"username":"Ibtissam1000","nom":"btissam","prenom":"elhabchi","date_naissence":null,"email":"<EMAIL>","profil":"/storage/profile_pic/default.jpg","cover":null,"sex":"homme","email_verified_at":null,"created_at":"2025-06-12T21:14:46.000000Z","updated_at":"2025-06-12T21:14:46.000000Z"} 
[2025-06-12 21:41:39] local.INFO: Final user_id set: 1  
[2025-06-12 21:41:39] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'id_user' in 'field list' (Connection: mysql, SQL: insert into `posts` (`description`, `location`, `id_user`, `map_url`, `updated_at`, `created_at`) values (jncsdjk, ?, 1, https://www.google.com/maps/search/?api=1&query=tanger, 2025-06-12 21:41:39, 2025-06-12 21:41:39)) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'id_user' in 'field list' (Connection: mysql, SQL: insert into `posts` (`description`, `location`, `id_user`, `map_url`, `updated_at`, `created_at`) values (jncsdjk, ?, 1, https://www.google.com/maps/search/?api=1&query=tanger, 2025-06-12 21:41:39, 2025-06-12 21:41:39)) at C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert()
#3 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId()
#4 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId()
#5 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call()
#6 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#7 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert()
#8 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\app\\Http\\Controllers\\PostController.php(64): Illuminate\\Database\\Eloquent\\Model->save()
#9 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\PostController->store()
#10 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#11 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#12 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#13 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#14 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#16 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#18 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#20 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#21 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#22 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#23 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#24 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#25 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#27 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#28 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#30 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#31 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#33 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#35 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#37 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#39 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#41 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#43 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#45 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#46 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#47 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#48 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#49 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'id_user' in 'field list' at C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:47)
[stacktrace]
#0 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(47): PDO->prepare()
#1 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}()
#2 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert()
#5 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId()
#6 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId()
#7 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call()
#8 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#9 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert()
#10 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\app\\Http\\Controllers\\PostController.php(64): Illuminate\\Database\\Eloquent\\Model->save()
#11 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\PostController->store()
#12 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#13 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#16 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#18 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#20 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#22 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#23 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#24 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#25 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#26 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#27 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#29 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#30 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#32 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#33 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#35 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#37 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#39 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#41 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#43 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#45 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#47 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#48 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#49 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#50 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#51 {main}
"} 
[2025-06-12 21:42:22] local.INFO: Auth check: true  
[2025-06-12 21:42:22] local.INFO: Auth ID: 1  
[2025-06-12 21:42:22] local.INFO: Auth user:  {"id":1,"username":"Ibtissam1000","nom":"btissam","prenom":"elhabchi","date_naissence":null,"email":"<EMAIL>","profil":"/storage/profile_pic/default.jpg","cover":null,"sex":"homme","email_verified_at":null,"created_at":"2025-06-12T21:14:46.000000Z","updated_at":"2025-06-12T21:14:46.000000Z"} 
[2025-06-12 21:42:22] local.INFO: Final user_id set: 1  
[2025-06-12 22:45:25] local.INFO: Auth check: true  
[2025-06-12 22:45:25] local.INFO: Auth ID: 2  
[2025-06-12 22:45:25] local.INFO: Auth user:  {"id":2,"username":"basma","nom":"basma","prenom":"elhabchi","date_naissence":null,"email":"<EMAIL>","profil":"/storage/profile_pic/default.jpg","cover":null,"sex":"homme","email_verified_at":null,"created_at":"2025-06-12T22:44:38.000000Z","updated_at":"2025-06-12T22:44:38.000000Z"} 
[2025-06-12 22:45:25] local.INFO: Final user_id set: 2  
[2025-06-12 22:47:37] local.INFO: Auth check: true  
[2025-06-12 22:47:37] local.INFO: Auth ID: 3  
[2025-06-12 22:47:37] local.INFO: Auth user:  {"id":3,"username":"btissam","nom":"btissam","prenom":"elhabchi","date_naissence":null,"email":"<EMAIL>","profil":"/storage/profile_pic/default.jpg","cover":null,"sex":"homme","email_verified_at":null,"created_at":"2025-06-12T22:46:54.000000Z","updated_at":"2025-06-12T22:46:54.000000Z"} 
[2025-06-12 22:47:37] local.INFO: Final user_id set: 3  
[2025-06-12 22:50:35] local.INFO: Auth check: true  
[2025-06-12 22:50:35] local.INFO: Auth ID: 4  
[2025-06-12 22:50:35] local.INFO: Auth user:  {"id":4,"username":"bsema","nom":"basema","prenom":"elhabchi","date_naissence":null,"email":"<EMAIL>","profil":"/storage/profile_pic/default.jpg","cover":null,"sex":"homme","email_verified_at":null,"created_at":"2025-06-12T22:49:49.000000Z","updated_at":"2025-06-12T22:49:49.000000Z"} 
[2025-06-12 22:50:35] local.INFO: Final user_id set: 4  
[2025-06-22 13:54:59] local.ERROR: syntax error, unexpected token "}", expecting ";" {"userId":1,"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"}\", expecting \";\" at C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\app\\Models\\Tag.php:148)
[stacktrace]
#0 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}()
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass()
#2 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\app\\Http\\Controllers\\TagController.php(42): method_exists()
#3 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\TagController->trending()
#4 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#5 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#6 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#7 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#8 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#9 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#10 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#11 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#12 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#14 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#15 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#16 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#17 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#18 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#19 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#21 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#22 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#24 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#25 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#27 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#29 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#31 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#33 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#35 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#37 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#39 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#40 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#41 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#42 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#43 {main}
"} 
[2025-06-22 13:55:00] local.ERROR: syntax error, unexpected token "}", expecting ";" {"userId":1,"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"}\", expecting \";\" at C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\app\\Models\\Tag.php:148)
[stacktrace]
#0 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}()
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass()
#2 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\app\\Http\\Controllers\\TagController.php(42): method_exists()
#3 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\TagController->trending()
#4 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#5 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#6 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#7 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#8 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#9 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#10 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#11 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#12 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#14 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#15 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#16 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#17 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#18 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#19 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#21 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#22 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#24 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#25 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#27 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#29 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#31 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#33 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#35 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#37 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#39 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#40 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#41 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#42 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#43 {main}
"} 
[2025-06-22 13:55:00] local.ERROR: syntax error, unexpected token "}", expecting ";" {"userId":1,"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"}\", expecting \";\" at C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\app\\Models\\Tag.php:148)
[stacktrace]
#0 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}()
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass()
#2 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\app\\Http\\Controllers\\TagController.php(42): method_exists()
#3 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\TagController->trending()
#4 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#5 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#6 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#7 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#8 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#9 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#10 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#11 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#12 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#14 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#15 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#16 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#17 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#18 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#19 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#21 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#22 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#24 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#25 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#27 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#29 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#31 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#33 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#35 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#37 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#39 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#40 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#41 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#42 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#43 {main}
"} 
[2025-06-22 13:55:01] local.ERROR: syntax error, unexpected token "}", expecting ";" {"userId":1,"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"}\", expecting \";\" at C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\app\\Models\\Tag.php:148)
[stacktrace]
#0 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}()
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass()
#2 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\app\\Http\\Controllers\\TagController.php(42): method_exists()
#3 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\TagController->trending()
#4 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#5 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#6 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#7 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#8 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#9 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#10 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#11 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#12 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#14 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#15 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#16 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#17 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#18 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#19 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#21 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#22 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#24 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#25 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#27 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#29 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#31 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#33 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#35 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#37 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#39 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#40 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#41 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#42 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#43 {main}
"} 
[2025-06-22 13:55:01] local.ERROR: syntax error, unexpected token "}", expecting ";" {"userId":1,"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"}\", expecting \";\" at C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\app\\Models\\Tag.php:148)
[stacktrace]
#0 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}()
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass()
#2 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\app\\Http\\Controllers\\TagController.php(42): method_exists()
#3 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\TagController->trending()
#4 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#5 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#6 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#7 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#8 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#9 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#10 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#11 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#12 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#14 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#15 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#16 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#17 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#18 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#19 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#21 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#22 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#24 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#25 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#27 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#29 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#31 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#33 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#35 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#37 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#39 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#40 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#41 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#42 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#43 {main}
"} 
[2025-06-22 13:55:02] local.ERROR: syntax error, unexpected token "}", expecting ";" {"userId":1,"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"}\", expecting \";\" at C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\app\\Models\\Tag.php:148)
[stacktrace]
#0 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}()
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass()
#2 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\app\\Http\\Controllers\\TagController.php(42): method_exists()
#3 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\TagController->trending()
#4 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#5 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#6 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#7 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#8 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#9 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#10 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#11 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#12 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#14 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#15 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#16 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#17 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#18 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#19 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#21 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#22 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#24 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#25 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#27 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#29 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#31 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#33 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#35 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#37 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#39 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#40 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#41 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#42 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#43 {main}
"} 
[2025-06-22 13:55:02] local.ERROR: syntax error, unexpected token "}", expecting ";" {"userId":1,"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"}\", expecting \";\" at C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\app\\Models\\Tag.php:148)
[stacktrace]
#0 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}()
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass()
#2 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\app\\Http\\Controllers\\TagController.php(42): method_exists()
#3 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\TagController->trending()
#4 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#5 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#6 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#7 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#8 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#9 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#10 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#11 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#12 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#14 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#15 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#16 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#17 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#18 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#19 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#21 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#22 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#24 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#25 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#27 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#29 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#31 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#33 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#35 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#37 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#39 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#40 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#41 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#42 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#43 {main}
"} 
[2025-06-22 13:55:02] local.ERROR: syntax error, unexpected token "}", expecting ";" {"userId":1,"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"}\", expecting \";\" at C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\app\\Models\\Tag.php:148)
[stacktrace]
#0 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}()
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass()
#2 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\app\\Http\\Controllers\\TagController.php(42): method_exists()
#3 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\TagController->trending()
#4 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#5 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#6 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#7 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#8 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#9 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#10 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#11 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#12 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#14 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#15 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#16 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#17 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#18 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#19 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#21 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#22 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#24 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#25 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#27 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#29 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#31 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#33 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#35 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#37 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#39 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#40 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#41 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#42 C:\\Users\\<USER>\\3D Objects\\tripy\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#43 {main}
"} 
